// Test script to verify randomization functionality

// Utility function to shuffle an array using Fisher-Yates algorithm
function shuffleArray(array) {
  const shuffled = [...array];
  for (let i = shuffled.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
  }
  return shuffled;
}

// Function to shuffle question options while maintaining correct answer tracking
function shuffleQuestionOptions(question) {
  if (!question.options || question.options.length <= 1) {
    return question;
  }

  // Create array of options with their original indices
  const optionsWithIndices = question.options.map((option, index) => ({
    option,
    originalIndex: index
  }));

  // Shuffle the options
  const shuffledOptionsWithIndices = shuffleArray(optionsWithIndices);

  // Extract shuffled options and find new correct answer index
  const shuffledOptions = shuffledOptionsWithIndices.map(item => item.option);
  const newCorrectAnswerIndex = shuffledOptionsWithIndices.findIndex(
    item => item.originalIndex === question.correctAnswer
  );

  return {
    ...question,
    options: shuffledOptions,
    correctAnswer: newCorrectAnswerIndex,
    originalCorrectAnswer: question.correctAnswer // Keep track of original for reference
  };
}

// Test data
const testQuestion = {
  id: 'q1',
  question: 'What is the capital of France?',
  options: [
    { text: 'London' },
    { text: 'Berlin' },
    { text: 'Paris' },
    { text: 'Madrid' }
  ],
  correctAnswer: 2 // Paris
};

const testQuestions = [
  {
    id: 'q1',
    question: 'Question 1',
    options: [{ text: 'A' }, { text: 'B' }, { text: 'C' }],
    correctAnswer: 1
  },
  {
    id: 'q2',
    question: 'Question 2',
    options: [{ text: 'X' }, { text: 'Y' }, { text: 'Z' }],
    correctAnswer: 0
  },
  {
    id: 'q3',
    question: 'Question 3',
    options: [{ text: '1' }, { text: '2' }, { text: '3' }],
    correctAnswer: 2
  }
];

console.log('=== Testing Question Option Shuffling ===');
console.log('Original question:');
console.log('Options:', testQuestion.options.map(o => o.text));
console.log('Correct answer index:', testQuestion.correctAnswer, '(' + testQuestion.options[testQuestion.correctAnswer].text + ')');

const shuffledQuestion = shuffleQuestionOptions(testQuestion);
console.log('\nShuffled question:');
console.log('Options:', shuffledQuestion.options.map(o => o.text));
console.log('New correct answer index:', shuffledQuestion.correctAnswer, '(' + shuffledQuestion.options[shuffledQuestion.correctAnswer].text + ')');
console.log('Original correct answer index:', shuffledQuestion.originalCorrectAnswer);

console.log('\n=== Testing Question Array Shuffling ===');
console.log('Original questions order:', testQuestions.map(q => q.id));

const shuffledQuestions = shuffleArray(testQuestions);
console.log('Shuffled questions order:', shuffledQuestions.map(q => q.id));

console.log('\n=== Testing Multiple Shuffles ===');
for (let i = 0; i < 3; i++) {
  const shuffled = shuffleQuestionOptions(testQuestion);
  console.log(`Shuffle ${i + 1}:`, shuffled.options.map(o => o.text), 'Correct:', shuffled.correctAnswer);
}

console.log('\n✅ Randomization test completed!');
